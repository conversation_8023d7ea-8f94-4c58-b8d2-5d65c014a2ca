# Makefile pour SEO AI Agents
# Usage: make test, make test-coverage, etc.

.PHONY: help test test-unit test-integration test-coverage test-html clean install

# Variables
PYTHON = python
PYTEST = $(PYTHON) -m pytest
TEST_DIR = tests/
SRC_DIR = .

help:  ## Afficher cette aide
	@echo "Commandes disponibles:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  \033[36m%-20s\033[0m %s\n", $$1, $$2}'

install:  ## Installer les dépendances
	pip install -r requirements.txt

test:  ## Exécuter tous les tests
	$(PYTEST) $(TEST_DIR) -v

test-unit:  ## Exécuter les tests unitaires
	$(PYTEST) $(TEST_DIR) -v -m unit

test-integration:  ## Exécuter les tests d'intégration
	$(PYTEST) $(TEST_DIR) -v -m integration

test-coverage:  ## Exécuter les tests avec couverture
	$(PYTEST) $(TEST_DIR) --cov=$(SRC_DIR) --cov-report=term-missing --cov-report=html

test-html:  ## Générer un rapport HTML des tests
	mkdir -p reports
	$(PYTEST) $(TEST_DIR) --html=reports/test_report.html --self-contained-html

test-web:  ## Exécuter les tests de web scraping
	$(PYTEST) tests/test_web_scrape.py -v

test-seo:  ## Exécuter les tests d'analyse SEO
	$(PYTEST) tests/test_seo_metrics.py -v

test-config:  ## Exécuter les tests de configuration
	$(PYTEST) tests/test_config.py -v

clean:  ## Nettoyer les fichiers temporaires
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf reports
	rm -rf .coverage

lint:  ## Vérifier le style du code (nécessite flake8)
	flake8 $(SRC_DIR) --exclude=venv,env,.git,__pycache__

format:  ## Formater le code (nécessite black)
	black $(SRC_DIR) --exclude="/(\.git|__pycache__|\.pytest_cache|venv|env)/"

check:  ## Vérifier le code (lint + tests)
	make lint
	make test
