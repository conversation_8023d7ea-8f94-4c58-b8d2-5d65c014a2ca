#!/usr/bin/env python3
"""
Script pour exécuter les tests du projet SEO AI Agents.

Usage:
    python run_tests.py                    # Tous les tests
    python run_tests.py --unit             # Tests unitaires seulement
    python run_tests.py --integration      # Tests d'intégration seulement
    python run_tests.py --coverage         # Avec rapport de couverture
    python run_tests.py --html             # Rapport HTML
    python run_tests.py --file test_web_scrape.py  # Fichier spécifique
"""

import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd):
    """Exécute une commande et affiche le résultat."""
    print(f"🚀 Exécution: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr, file=sys.stderr)
    
    return result.returncode


def main():
    parser = argparse.ArgumentParser(description="Exécuter les tests SEO AI Agents")
    parser.add_argument("--unit", action="store_true", help="Tests unitaires seulement")
    parser.add_argument("--integration", action="store_true", help="Tests d'intégration seulement")
    parser.add_argument("--coverage", action="store_true", help="Avec rapport de couverture")
    parser.add_argument("--html", action="store_true", help="Rapport HTML")
    parser.add_argument("--file", help="Fichier de test spécifique")
    parser.add_argument("--verbose", "-v", action="store_true", help="Mode verbeux")
    
    args = parser.parse_args()
    
    # Construction de la commande pytest
    cmd = ["python", "-m", "pytest"]
    
    # Ajout des options selon les arguments
    if args.verbose:
        cmd.append("-v")
    
    if args.coverage:
        cmd.extend(["--cov=.", "--cov-report=term-missing"])
    
    if args.html:
        cmd.extend(["--html=reports/test_report.html", "--self-contained-html"])
        # Créer le répertoire reports s'il n'existe pas
        Path("reports").mkdir(exist_ok=True)
    
    if args.unit:
        cmd.extend(["-m", "unit"])
    elif args.integration:
        cmd.extend(["-m", "integration"])
    
    if args.file:
        cmd.append(f"tests/{args.file}")
    else:
        cmd.append("tests/")
    
    # Exécution des tests
    print("🧪 Lancement des tests SEO AI Agents")
    print("=" * 50)
    
    exit_code = run_command(cmd)
    
    if exit_code == 0:
        print("\n✅ Tous les tests sont passés avec succès!")
    else:
        print(f"\n❌ Certains tests ont échoué (code de sortie: {exit_code})")
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())
