"""
Tests pour les fonctions d'analyse SEO.
"""
import unittest
import pytest
from seo_ai_agents import analyze_seo_metrics


class TestSEOMetrics(unittest.TestCase):
    """Tests unitaires pour l'analyse des métriques SEO."""
    
    def test_analyze_seo_metrics_basic(self):
        """Test de l'analyse SEO avec du contenu basique."""
        content = """
# Titre principal
## Sous-titre 1
### Sous-titre 2
Ceci est un contenu de test avec des [liens](http://example.com) et du texte.
## Autre sous-titre
Plus de contenu pour tester les métriques SEO.
"""
        result = analyze_seo_metrics.invoke(content)
        
        # Vérifier que le résultat contient les métriques attendues
        self.assertIn("Métriques SEO basiques", result)
        self.assertIn("Nombre de mots:", result)
        self.assertIn("Titres H1:", result)
        self.assertIn("Titres H2:", result)
        self.assertIn("Titres H3:", result)
        self.assertIn("Liens détectés:", result)
    
    def test_analyze_seo_metrics_word_count(self):
        """Test du comptage de mots."""
        content = "Un deux trois quatre cinq mots exactement"
        result = analyze_seo_metrics.invoke(content)
        
        # Vérifier le nombre de mots
        self.assertIn("Nombre de mots: 6", result)
    
    def test_analyze_seo_metrics_headers(self):
        """Test du comptage des en-têtes."""
        content = """
# H1 Premier
# H1 Deuxième
## H2 Premier
## H2 Deuxième
## H2 Troisième
### H3 Premier
"""
        result = analyze_seo_metrics.invoke(content)
        
        # Vérifier le comptage des en-têtes
        self.assertIn("Titres H1: 2", result)
        self.assertIn("Titres H2: 3", result)
        self.assertIn("Titres H3: 1", result)
    
    def test_analyze_seo_metrics_links(self):
        """Test du comptage des liens."""
        content = """
Voici un [premier lien](http://example1.com) et un [deuxième lien](http://example2.com).
Et encore un [troisième lien](http://example3.com).
"""
        result = analyze_seo_metrics.invoke(content)
        
        # Vérifier le comptage des liens
        self.assertIn("Liens détectés: 3", result)
    
    def test_analyze_seo_metrics_empty_content(self):
        """Test avec du contenu vide."""
        content = ""
        result = analyze_seo_metrics.invoke(content)
        
        # Vérifier les valeurs par défaut
        self.assertIn("Nombre de mots: 0", result)
        self.assertIn("Titres H1: 0", result)
        self.assertIn("Titres H2: 0", result)
        self.assertIn("Titres H3: 0", result)
        self.assertIn("Liens détectés: 0", result)


# Tests avec pytest
def test_seo_metrics_with_pytest(sample_content):
    """Test pytest pour analyze_seo_metrics."""
    result = analyze_seo_metrics.invoke(sample_content)
    assert "Métriques SEO basiques" in result
    assert "Nombre de mots:" in result


if __name__ == "__main__":
    unittest.main()
