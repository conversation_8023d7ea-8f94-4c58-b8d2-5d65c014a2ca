"""
Tests pour la fonction web_scrape.
"""
import unittest
import re
import pytest
from seo_ai_agents import web_scrape


def extract_title(text):
    """
    Extracts the title from a string containing structured text.

    :param text: The input string containing the title.
    :return: The extracted title string, or None if the title is not found.
    """
    match = re.search(r'Title : (.*) \(', text)
    return match.group(1).strip() if match else None


class TestWebScrape(unittest.TestCase):
    """Tests unitaires pour la fonction web_scrape."""
    
    def test_web_scrape_valid_url(self):
        """Test du web scraping avec une URL valide."""
        url = "https://toscrape.com/"
        content = web_scrape.invoke(url)
        
        # Vérifier que le titre est extrait correctement
        title = extract_title(content)
        self.assertEqual("Web Scraping Sandbox", title)
        
        # Vérifier la longueur du contenu (peut varier légèrement)
        self.assertGreater(len(content), 900)
        self.assertLess(len(content), 1100)
    
    def test_web_scrape_invalid_url(self):
        """Test du web scraping avec une URL invalide."""
        url = "https://url-inexistante-test-123456.com/"
        content = web_scrape.invoke(url)
        
        # Vérifier qu'une erreur est retournée
        self.assertIn("❌ Erreur", content)
    
    def test_extract_title_valid_content(self):
        """Test de l'extraction de titre avec du contenu valide."""
        test_content = "## Site Name : Example - Title : Test Title (https://example.com)\n\nContent here"
        title = extract_title(test_content)
        self.assertEqual("Test Title", title)
    
    def test_extract_title_no_title(self):
        """Test de l'extraction de titre sans titre présent."""
        test_content = "Contenu sans titre formaté"
        title = extract_title(test_content)
        self.assertIsNone(title)


# Tests avec pytest
def test_web_scrape_with_pytest(sample_url):
    """Test pytest pour web_scrape."""
    content = web_scrape.invoke(sample_url)
    assert "Web Scraping Sandbox" in content
    assert len(content) > 900


if __name__ == "__main__":
    unittest.main()
